import Button from '@mui/material/Button';
import type { ChildrenType } from '@core/types';
import LayoutWrapper from '@layouts/LayoutWrapper';
import VerticalLayout from '@layouts/VerticalLayout';
import HorizontalLayout from '@layouts/HorizontalLayout';
import Providers from '@components/Providers';
import Navigation from '@components/layout/vertical/Navigation';
import Header from '@components/layout/horizontal/Header';
import Navbar from '@components/layout/vertical/Navbar';
import VerticalFooter from '@components/layout/vertical/Footer';
import HorizontalFooter from '@components/layout/horizontal/Footer';
import ScrollToTop from '@core/components/scroll-to-top';
import { getMode, getSystemMode } from '@core/utils/serverHelpers';
import AuthRefreshHandler from '@/components/AuthRefreshHandler';
import { AbilityProvider } from '@/libs/casl/AbilityContext';
import ClientLayoutWrapper from './ClientLayoutWrapper';
import NextTopLoader from 'nextjs-toploader';

const Layout = async (props: ChildrenType) => {
  const { children } = props;
  const direction = 'ltr';
  const mode = await getMode();
  const systemMode = await getSystemMode();

  return (
    <>
      <NextTopLoader color="#8C57FF" />

      <AuthRefreshHandler />
      <Providers direction={direction}>
        <ClientLayoutWrapper>
          <AbilityProvider>
            <LayoutWrapper
              systemMode={systemMode}
              verticalLayout={
                <VerticalLayout
                  navigation={<Navigation mode={mode} />}
                  navbar={<Navbar />}
                  footer={<VerticalFooter />}
                >
                  {children}
                </VerticalLayout>
              }
              horizontalLayout={
                <HorizontalLayout header={<Header />} footer={<HorizontalFooter />}>
                  {children}
                </HorizontalLayout>
              }
            />
            <ScrollToTop className="mui-fixed">
              <Button
                variant="contained"
                className="is-10 bs-10 rounded-full p-0 min-is-0 flex items-center justify-center"
              >
                <i className="ri-arrow-up-line" />
              </Button>
            </ScrollToTop>
          </AbilityProvider>
        </ClientLayoutWrapper>
      </Providers>
    </>
  );
};

export default Layout;
