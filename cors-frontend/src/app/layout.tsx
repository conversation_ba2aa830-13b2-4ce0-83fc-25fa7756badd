import InitColorSchemeScript from '@mui/material/InitColorSchemeScript';
import NextTopLoader from 'nextjs-toploader';

import 'react-perfect-scrollbar/dist/css/styles.css';

import type { ChildrenType } from '@core/types';

import { getSystemMode } from '@core/utils/serverHelpers';

import '@/app/globals.css';

import '@assets/iconify-icons/generated-icons.css';

export const metadata = {
  title: 'CORS',
  description: 'Central Order & Routing System.',
};

const RootLayout = async (props: ChildrenType) => {
  const { children } = props;

  const systemMode = await getSystemMode();
  const direction = 'ltr';

  return (
    <html id="__next" lang="en" dir={direction} suppressHydrationWarning>
      <head suppressHydrationWarning>
        <link
          rel="icon"
          href="https://cdn.shopify.com/s/files/1/0537/2585/5916/files/Cuddle_Clones_Logo.png?v=**********"
        />
      </head>
      <body className="flex is-full min-bs-full flex-auto flex-col" suppressHydrationWarning>
        <InitColorSchemeScript attribute="data" defaultMode={systemMode} />
        <NextTopLoader color="#8C57FF" showSpinner={false} height={3} crawl={true} />
        {children}
      </body>
    </html>
  );
};

export default RootLayout;
