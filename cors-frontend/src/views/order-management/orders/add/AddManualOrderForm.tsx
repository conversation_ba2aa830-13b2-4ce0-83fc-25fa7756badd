'use client';
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  Autocomplete,
  Box,
  Card,
  CardContent,
  TextField,
  Typography,
  Button,
  IconButton,
  Divider,
  debounce,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import DeleteIcon from '@mui/icons-material/Delete';
import React, { useState, useCallback, useEffect } from 'react';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { orderFormSchema, OrderFormSchemaType } from './validations/manual-order.validation';
import SingleProductForm from './components/SingleProductForm';
import CustomizerProduct from './components/CustomizerProduct';
import { OrderItemType, SingleProduct, SingleSKU } from '@/types/manual-order.type';
import { apiCall } from '@/actions/common.actions';
import PdpCustomizerProducts from './components/PdpCustomizerProducts';
import CommonUserDetailsForm from './components/CommonUserDetailsForm';
import {
  finalLineItemsPayload,
  formatOrderItemsWithAddOn,
  getCustomerPayload,
  getPetsCountFromSKU,
} from '@/utils/manual-order.utils';
import useApiCall from '@/hooks/useApiCall';

export default function AddManualOrderForm() {
  const { makeRequest: requestSkuDefaultOptions, isLoading: isLoadingSkuDefaultOptions } =
    useApiCall<{
      data: SingleSKU[];
    }>(`/product-sku/?page=1&limit=25`, 'get', false);

  const [selectedSKUs, setSelectedSKUs] = useState<SingleSKU[]>([]);
  const [skuOptions, setSkuOptions] = useState<SingleSKU[]>([]);
  const [skuDefaultOptions, setSkuDefaultOptions] = useState<SingleSKU[]>([]);
  const [showDefaultOptions, setShowDefaultOptions] = useState<boolean>(true);

  const [selectedProducts, setSelectedProducts] = useState<SingleProduct[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchData = async () => {
      const data = await requestSkuDefaultOptions();
      setSkuDefaultOptions(data?.data || []);
    };
    if (!skuDefaultOptions?.length) {
      fetchData();
    }
  }, [skuDefaultOptions]);
  const {
    register,
    handleSubmit,
    control,
    setValue,
    trigger,
    formState: { errors, isSubmitting },
  } = useForm<OrderFormSchemaType>({
    resolver: yupResolver(orderFormSchema) as any,
    defaultValues: {
      orderItems: [
        {
          sku: undefined,
          quantity: 1,
          petImages: [],
          product: undefined,
          identifier: [],
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'orderItems',
  });
  const debouncedFetchSKUs = useCallback(
    debounce(async (inputValue: string) => {
      setShowDefaultOptions(false);

      if (!inputValue) {
        setSkuOptions([]);
        return;
      }
      setLoading(true);
      try {
        const skus = await apiCall<{ data: SingleSKU[] }>(
          'get',
          `/product-sku?q=sku:like:${inputValue}`,
        );

        setSkuOptions(skus?.data);
      } catch (error) {
        console.error('Error fetching SKUs:', error);
        setSkuOptions([]);
      } finally {
        setLoading(false);
      }
    }, 1000),
    [],
  );

  const onSubmit = async (data: OrderFormSchemaType) => {
    const payload = getCustomerPayload(data);
    const allIdentifiers = data?.orderItems?.reduce((acc: any[], item: OrderItemType) => {
      if (item.identifier && Array.isArray(item.identifier)) {
        return [...acc, ...item.identifier];
      }
      return acc;
    }, []);

    const allIdentifiersPlush = data?.orderItems?.reduce((acc: any[], item: OrderItemType) => {
      if (item.finalOptions && Array.isArray(item.finalOptions)) {
        return [...acc, ...item.finalOptions];
      }
      return acc;
    }, []);
    const allIdentifiersPdp = data?.orderItems?.reduce((acc: any[], item: OrderItemType) => {
      if (item.pdp_extras && Array.isArray(item.pdp_extras)) {
        return [...acc, ...item.pdp_extras];
      }
      return acc;
    }, []);
    const formattedOrderItems = formatOrderItemsWithAddOn(data, allIdentifiers);
    const line_items = finalLineItemsPayload(
      formattedOrderItems,
      allIdentifiers,
      allIdentifiersPlush,
      allIdentifiersPdp,
    );

    const dataToSend = {
      ...payload,
      line_items: line_items,
    };
    // apiCall('post', 'orders/manual-order', dataToSend);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Add Manual Order
      </Typography>

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <CommonUserDetailsForm control={control} errors={errors} register={register} />

            <Typography variant="h3" sx={{ my: 5, textAlign: 'center' }}>
              Order Items
            </Typography>
            {fields.map((field, index) => (
              <React.Fragment key={field.id + index}>
                <Grid
                  size={12}
                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <Typography variant="h5" sx={{ my: 2 }}>
                    Order Item {index + 1}
                  </Typography>
                  <IconButton
                    onClick={() => {
                      if (fields.length === 1) {
                        return;
                      }
                      remove(index);
                    }}
                    color="error"
                    disabled={fields.length === 1}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Grid>
                <Grid container spacing={3} className="mt-4">
                  <Grid size={{ xs: 12, lg: 6 }}>
                    <Controller
                      name={`orderItems.${index}.sku`}
                      control={control}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          fullWidth
                          value={field.value || ''}
                          options={showDefaultOptions ? skuDefaultOptions : skuOptions}
                          getOptionLabel={(option: SingleSKU) => option.sku ?? ''}
                          renderInput={params => (
                            <TextField
                              {...params}
                              label="SKU"
                              error={!!errors.orderItems?.[index]?.sku}
                              helperText={errors.orderItems?.[index]?.sku?.message}
                            />
                          )}
                          onChange={async (_, value) => {
                            field.onChange(value);
                            if (value?.id) {
                              const skuData = await apiCall<SingleSKU>(
                                'get',
                                `/product-sku/${value?.id}`,
                              );
                              if (skuData) {
                                const newSelectedSKUs = [...selectedSKUs];
                                newSelectedSKUs[index] = skuData;
                                setSelectedSKUs(newSelectedSKUs);
                                const petCount = getPetsCountFromSKU(value?.sku);
                                setValue(`orderItems.${index}.product`, null);
                                const newSelectedProducts = [...selectedProducts];
                                newSelectedProducts.splice(index, 1);
                                setSelectedProducts(newSelectedProducts);
                              }
                            } else {
                              setSelectedSKUs([]);
                              setSelectedProducts([]);
                              setSkuOptions([]);
                            }
                          }}
                          onInputChange={(_, newInputValue) => {
                            if (newInputValue) {
                              debouncedFetchSKUs(newInputValue);
                            }
                          }}
                          loading={loading}
                        />
                      )}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, lg: 6 }}>
                    <Controller
                      name={`orderItems.${index}.product`}
                      control={control}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          fullWidth
                          value={field.value || ''}
                          options={
                            selectedSKUs[index]?.products?.map((product: SingleProduct) => ({
                              ...product,
                              key: `${product.id}-${index}-${product.name}`,
                            })) || []
                          }
                          getOptionLabel={(option: SingleProduct) => option.name ?? ''}
                          noOptionsText="No Products Found (Please select SKU first)"
                          renderInput={params => (
                            <TextField
                              {...params}
                              label="Product"
                              error={!!errors.orderItems?.[index]?.product}
                              helperText={errors.orderItems?.[index]?.product?.message}
                            />
                          )}
                          onChange={(_, value) => {
                            const newValue: any = {
                              ...value,
                              timestampkey: String(Date.now()),
                            };
                            field.onChange(newValue);
                            const newSelectedProducts = [...selectedProducts];
                            newSelectedProducts[index] = newValue;
                            setSelectedProducts(newSelectedProducts);
                          }}
                          loading={loading}
                        />
                      )}
                    />
                  </Grid>
                  <Grid size={12}>
                    {selectedProducts[index]?.metadata?.product_customizer ? (
                      <CustomizerProduct
                        selectedProduct={selectedProducts[index]}
                        index={index}
                        control={control}
                        errors={errors}
                        setValue={setValue}
                        trigger={trigger}
                      />
                    ) : selectedProducts[index]?.metadata?.pdp_customizer ? (
                      <PdpCustomizerProducts
                        selectedProduct={selectedProducts[index]}
                        index={index}
                        control={control}
                        errors={errors}
                        setValue={setValue}
                        trigger={trigger}
                      />
                    ) : (
                      <SingleProductForm
                        selectedProduct={selectedProducts[index]}
                        selectedSKU={selectedSKUs[index]}
                        index={index}
                        control={control}
                        errors={errors}
                        setValue={setValue}
                      />
                    )}
                  </Grid>
                </Grid>
                <Grid size={12}>
                  <Divider className="my-9" />
                </Grid>
              </React.Fragment>
            ))}
            <Grid size={12} sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
              <Button
                onClick={() =>
                  append({
                    sku: undefined,
                    quantity: 1,
                    petImages: [],
                    product: undefined,
                    identifier: [],
                  })
                }
                variant="outlined"
              >
                Add Item
              </Button>
            </Grid>

            <Grid size={12} sx={{ mt: 4 }}>
              <LoadingButton
                type="submit"
                variant="contained"
                loading={isSubmitting}
                size="medium"
                disabled={isSubmitting || loading}
              >
                Create Order
              </LoadingButton>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
}
