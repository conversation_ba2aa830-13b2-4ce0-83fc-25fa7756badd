'use client';
import React from 'react';
import Grid from '@mui/material/Grid2';
import {
  Typo<PERSON>,
  Box,
  TextField,
  FormControl,
  FormHelperText,
  Button,
  RadioGroup,
  IconButton,
} from '@mui/material';
import {
  Control,
  Controller,
  FieldErrors,
  useFieldArray,
  UseFormSetValue,
  UseFormTrigger,
  useWatch,
} from 'react-hook-form';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import { Delete } from '@mui/icons-material';
import { OrderFormSchemaType } from '../../validations/manual-order.validation';

interface PetDetailsSectionProps {
  detailSection: any;
  index: number;
  control: Control<OrderFormSchemaType>;
  errors: FieldErrors<OrderFormSchemaType>;
  setValue: UseFormSetValue<OrderFormSchemaType>;
  errorsMap: any;
  trigger: UseFormTrigger<OrderFormSchemaType>;
}

const PetDetailsSection = ({
  detailSection,
  index,
  control,
  errors,
  setValue,
  errorsMap,
}: PetDetailsSectionProps) => {
  const {
    fields: uniqueCharacteristicsFields,
    append: appendUniqueCharacteristic,
    remove: removeUniqueCharacteristic,
  } = useFieldArray({
    control,
    name: `orderItems.${index}.uniqueCharacteristics`,
  });

  const {
    fields: questionsFields,
    append: appendQuestion,
    replace: replaceQuestion,
    update: updateQuestion,
  } = useFieldArray({
    control,
    name: `orderItems.${index}.customizer_questions`,
  });

  const petImagesCustomizerField = useWatch({
    control,
    name: `orderItems.${index}.petImagesCustomizer`,
  });
  return (
    <>
      <Grid size={12}>
        <Typography variant="h4" sx={{ my: 5 }}>
          {detailSection.main_heading}
        </Typography>
      </Grid>
      <Grid size={12}>
        <ImageUploadField
          control={control}
          errors={errors}
          setValue={setValue}
          name={`orderItems.${index}.petImagesCustomizer`}
          minImages={detailSection?.require_images}
          maxImages={10}
          errorField={errors?.orderItems?.length ? errorsMap?.petImagesCustomizer?.message : ''}
          formValue={petImagesCustomizerField}
          onChange={async () => {
            if (detailSection?.questions) {
              const newQuestions = detailSection?.questions?.map(
                (question: Record<'question_text' | 'label', string>, questionIndex: number) => {
                  return {
                    label: question.label.replace(/\s+/g, '_').toLowerCase(),
                    value: '',
                  };
                },
              );
              replaceQuestion(newQuestions);
            } else {
              replaceQuestion([]);
            }
            // await trigger(`orderItems.${index}.questions`);
          }}
        />
      </Grid>

      {petImagesCustomizerField?.length >= detailSection?.require_images &&
        detailSection?.questions &&
        detailSection?.questions?.map(
          (question: Record<'question_text' | 'label', string>, questionIndex: number) => {
            if (questionsFields.length <= questionIndex) {
              appendQuestion({
                label: question.label.replace(/\s+/g, '_').toLowerCase(),
                value: '',
              });
            }

            return (
              <Grid size={12} key={questionIndex}>
                <Typography variant="h5" className="mb-4 mt-6">
                  {question.question_text}
                </Typography>
                <Controller
                  name={`orderItems.${index}.customizer_questions.${questionIndex}.value`}
                  control={control}
                  rules={{ required: `${question.label} is required` }}
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth error={!!error}>
                      <RadioGroup {...field} sx={{ display: 'flex', flexDirection: 'row', gap: 4 }}>
                        {petImagesCustomizerField.map((_: any, imageIndex: number) => (
                          <Box
                            key={imageIndex}
                            onClick={() => {
                              field.onChange(petImagesCustomizerField[imageIndex]);
                              updateQuestion(questionIndex, {
                                label: question.label.replace(/\s+/g, '_').toLowerCase(),
                                value: petImagesCustomizerField[imageIndex],
                              });
                            }}
                            sx={{
                              cursor: 'pointer',
                              border:
                                (questionsFields[questionIndex] as Record<string, string>)
                                  ?.value === petImagesCustomizerField[imageIndex]
                                  ? '2px solid #1976d2'
                                  : '2px solid transparent',
                              borderRadius: '4px',
                              p: 1,
                            }}
                          >
                            <img
                              src={petImagesCustomizerField[imageIndex]}
                              alt={`Pet Image ${imageIndex + 1}`}
                              style={{
                                width: '100px',
                                height: '100px',
                                objectFit: 'cover',
                              }}
                            />
                            <Typography>Image {imageIndex + 1}</Typography>
                          </Box>
                        ))}
                      </RadioGroup>
                      <FormHelperText>{error?.message}</FormHelperText>
                    </FormControl>
                  )}
                />
              </Grid>
            );
          },
        )}
      <Grid size={12}>
        <Typography variant="h4" sx={{ my: 5 }}>
          {detailSection.unique_characteristics_heading}
        </Typography>
      </Grid>
      {uniqueCharacteristicsFields.map((field, characteristicIndex) => (
        <Grid size={12} container spacing={2} alignItems="center" key={field.id}>
          <Grid size={12}>
            <div className="flex justify-between items-center">
              <Typography variant="h5" className="mb-4 mt-6">
                Characteristic {characteristicIndex + 1}
              </Typography>
              <IconButton
                onClick={() => removeUniqueCharacteristic(characteristicIndex)}
                color="error"
              >
                <Delete />
              </IconButton>
            </div>
          </Grid>
          <Grid size={12}>
            <Controller
              name={`orderItems.${index}.uniqueCharacteristics.${characteristicIndex}.description`}
              control={control}
              rules={{ required: 'Description is required' }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  helperText={error?.message}
                  multiline
                  rows={2}
                  {...field}
                  fullWidth
                  label="Unique Characteristic Description"
                  variant="outlined"
                />
              )}
            />
          </Grid>
          <Grid size={12}>
            <Controller
              name={`orderItems.${index}.uniqueCharacteristics.${characteristicIndex}.image`}
              control={control}
              rules={{ required: 'Image is required' }}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <ImageUploadField
                  control={control}
                  errors={errors}
                  setValue={setValue}
                  name={`orderItems.${index}.uniqueCharacteristics.${characteristicIndex}.image`}
                  minImages={1}
                  maxImages={1}
                  formValue={value}
                  errorField={error?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, lg: 1 }}></Grid>
        </Grid>
      ))}
      <Grid size={12}>
        <Button
          variant="outlined"
          onClick={() => appendUniqueCharacteristic({ description: '', image: [] })}
        >
          Add Unique Characteristic
        </Button>
      </Grid>
    </>
  );
};

export default PetDetailsSection;
