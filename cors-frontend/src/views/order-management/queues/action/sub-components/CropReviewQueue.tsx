'use client';
import Button from '@/components/Button';
import { Box } from '@mui/material';
import Grid from '@mui/material/Grid2';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { SingleQueueItem } from '@/types/queues.types';
import LoadingView from '@/components/LoadingView';
import { toast } from 'react-toastify';
import useApiCall from '@/hooks/useApiCall';
import ProductAttributesViewer from '../../components/ProductAttributesViewer';

const CropReviewQueue = ({
  queueItem,
  queueId,
  actionType,
}: {
  queueItem: SingleQueueItem | undefined;
  queueId: string;
  actionType: string;
}) => {
  const router = useRouter();
  const [upcomingQueue, setUpcomingQueue] = useState<SingleQueueItem | undefined>(undefined);
  const { isLoading, makeRequest: requestUpdateQueueItem } = useApiCall<SingleQueueItem>(
    `/workflow-queues/update-queue-item-status`,
    'put',
    false,
    {
      body: {
        type: 'attachment',
        itemId: queueItem?.attachments?.id,
        queueId: queueId,
      },
    },
  );
  useEffect(() => {
    if (upcomingQueue) {
      if (upcomingQueue?.attachments == null) {
        router.back();
      }
    }
  }, [upcomingQueue, router]);

  const handleUpdateQueueItem = async (type: string) => {
    try {
      const response = await requestUpdateQueueItem({
        body: {
          action: type,
          type: 'attachment',
          itemId: queueItem?.attachments?.id,
          queueId: queueId,
        },
      });
      if (response) {
        setUpcomingQueue(response);
        toast.success('Queue item updated successfully');
      }
    } catch (error) {
      toast.error('Failed to update queue item. Please try again.');
      console.error('Error updating queue item:', error);
    }
  };
  if (isLoading) return <LoadingView />;

  if (upcomingQueue?.attachments) {
    return <CropReviewQueue queueItem={upcomingQueue} queueId={queueId} actionType={actionType} />;
  }

  return (
    <>
      <ProductAttributesViewer actionType={actionType} queueItem={queueItem} />

      <Grid container spacing={4} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 6 }}>
          <SingleImageViewCard
            imageUrl={queueItem?.attachments?.attachment_url || ''}
            title="Customer Image"
            downloadUrl={queueItem?.attachments?.attachment_url || ''}
            imageName={queueItem?.attachments?.attachment_url?.split('/').pop() || ''}
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <SingleImageViewCard
            imageUrl={queueItem?.attachments?.cutout_pro_url || ''}
            title="Cutout Pro Image"
            downloadUrl={queueItem?.attachments?.cutout_pro_url || ''}
            imageName={queueItem?.attachments?.cutout_pro_url?.split('/').pop() || ''}
          />
        </Grid>
      </Grid>

      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 4 }}>
        <Button
          variant="outlined"
          color="warning"
          fullWidth
          size="small"
          title="Request New Image"
          onClick={() => handleUpdateQueueItem('new-image-requested')}
          disabled={isLoading}
          sx={{ width: '250px' }}
        />
        <Button
          variant="outlined"
          color="error"
          fullWidth
          size="small"
          title="Deny Crop"
          onClick={() => handleUpdateQueueItem('deny')}
          disabled={isLoading}
          sx={{ width: '250px' }}
        />
        <Button
          variant="outlined"
          fullWidth
          size="small"
          title="Approve Crop"
          onClick={() => handleUpdateQueueItem('approve')}
          disabled={isLoading}
          sx={{ width: '250px' }}
        />
      </Box>

      {/* <Box maxWidth="600px" mx="auto">
        <Grid container spacing={2} justifyContent="center">
          <Grid size={{ xs: 12, sm: 4 }}>
            <Button
              variant="outlined"
              color="warning"
              fullWidth
              size="small"
              title="Request New Image"
              onClick={() => handleUpdateQueueItem('new-image-requested')}
              disabled={isLoading}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 4 }}>
            <Button
              variant="outlined"
              fullWidth
              size="small"
              title="Approve Crop"
              onClick={() => handleUpdateQueueItem('approve')}
              disabled={isLoading}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 4 }}>
            <Button
              variant="outlined"
              color="error"
              fullWidth
              size="small"
              title="Deny Crop"
              onClick={() => handleUpdateQueueItem('deny')}
              disabled={isLoading}
            />
          </Grid>
        </Grid>
      </Box> */}
    </>
  );
};

export default CropReviewQueue;
