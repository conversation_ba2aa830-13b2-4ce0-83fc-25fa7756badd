import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Box, TextField } from '@mui/material';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import Button from '@/components/Button';
type FormType = {
  image: string[];
  request: string;
};
const AddArtworkRequest = () => {
  const {
    handleSubmit,
    control,
    register,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one art file is required')
          .max(1, 'Only one art file is allowed')
          .required('Art file is required'),
        request: yup.string().required('Request is required'),
      }),
    ),
    defaultValues: {
      image: [],
      request: '',
    },
  });

  const onSubmit = (data: FormType) => {
    // console.log(data);
  };

  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <TextField
          fullWidth
          multiline
          rows={4}
          label="Reason"
          {...register('request')}
          error={!!errors.request}
          helperText={errors.request?.message}
          sx={{ my: 4 }}
        />{' '}
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              key="image-upload-field"
              control={control}
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={1}
              formValue={value}
              errorField={error?.message}
              title="Upload Art File"
              buttonText="Upload"
            />
          )}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
          <Button
            variant="outlined"
            fullWidth
            size="small"
            title="Add Request"
            type="submit"
            disabled={isSubmitting}
            sx={{ width: '150px' }}
          />
        </Box>
      </form>
    </div>
  );
};

export default AddArtworkRequest;
