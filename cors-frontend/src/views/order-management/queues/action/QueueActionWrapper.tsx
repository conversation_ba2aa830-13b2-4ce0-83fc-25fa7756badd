'use client';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography } from '@mui/material';
import CropNeededQueue from './sub-components/CropNeededQueue';
import CropReviewQueue from './sub-components/CropReviewQueue';
import TemplatePlacementQueue from './sub-components/TemplatePlacementQueue';
import ArtworkQueue from './sub-components/ArtworkQueue';
import LoadingView from '@/components/LoadingView';
import { SingleQueueItem } from '@/types/queues.types';
import useApiCall from '@/hooks/useApiCall';
import { useRouter } from 'next/navigation';
import { useAbility } from '@/libs/casl/AbilityContext';
import { Actions } from '@/libs/casl/ability';

const QueueActionWrapper = ({ actionType, queueId }: { actionType: string; queueId: string }) => {
  const ability = useAbility();
  const router = useRouter();

  const hasStopPermission = () => {
    const stopPermission = `Start/Stop ${actionType}`?.toString();
    return ability?.can(stopPermission as Actions, actionType);
  };

  const { data: queueItem, isLoading } = useApiCall<SingleQueueItem>(
    `/workflow-queues/get-queue-item/`,
    'get',
    true,
    { queryParams: { queueId } },
  );
  const {
    isLoading: isLoadingEndAction,
    makeRequest: requestEndAction,
    isSuccess: isSuccessEndAction,
  } = useApiCall<SingleQueueItem>(`/workflow-queues/stop-review/`, 'get', false, {
    queryParams: { queueId },
  });

  if (isSuccessEndAction) {
    router.back();
  }
  if (isLoading) return <LoadingView />;
  return (
    <Card>
      <CardContent>
        <Typography variant="h5" sx={{ mb: 4 }}>
          {actionType}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            justifyContent: 'space-between',
            alignItems: 'center',
            my: 4,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar>
              {queueItem?.attachments?.assigned_to?.charAt(0) ||
                queueItem?.lineItems?.assigned_to?.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="h6">
                {queueItem?.attachments?.assigned_to || queueItem?.lineItems?.assigned_to}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              color="error"
              variant="outlined"
              onClick={async () => {
                if (!queueId) return;
                if (actionType?.toLowerCase()?.includes('artwork revision')) {
                  router.back();
                } else {
                  requestEndAction();
                }
              }}
              disabled={isLoading || isLoadingEndAction || !hasStopPermission()}
              title={!hasStopPermission() ? "You don't have permission to stop this queue" : ''}
              sx={{ width: '100px' }}
            >
              {isLoadingEndAction ? 'Stopping...' : 'Stop'}
            </Button>
          </Box>
        </Box>

        {(() => {
          const queueProps = {
            queueItem,
            queueId,
            actionType,
          };

          switch (actionType?.toLowerCase()) {
            case 'crop needed':
              return <CropNeededQueue {...queueProps} />;
            case 'crop review':
              return <CropReviewQueue {...queueProps} />;
            case 'template placement':
              return <TemplatePlacementQueue {...queueProps} />;
            default:
              if (
                actionType?.toLowerCase()?.includes('ready for artwork') ||
                actionType?.toLowerCase()?.includes('artwork revision')
              ) {
                return <ArtworkQueue {...queueProps} />;
              }
              return <></>;
          }
        })()}
      </CardContent>
    </Card>
  );
};

export default QueueActionWrapper;
