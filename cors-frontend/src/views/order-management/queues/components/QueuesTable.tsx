import DataTable from '@/components/Datatable';
import { useRouter, useSearchParams } from 'next/navigation';

const QueuesTable = ({
  data,
  columns,
  loading,
  queue,
  page,
  limit,
  setPage,
  setLimit,
}: {
  data: any;
  columns: any;
  loading: boolean;
  queue: any;
  page: number;
  limit: number;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  return (
    <div>
      {' '}
      <DataTable
        data={data}
        columns={columns || []}
        enableFilters={false}
        loading={loading}
        totalCount={queue?.item_count || 0}
        page={Number(page)}
        initialPageSize={Number(limit)}
        limit={Number(limit)}
        onPaginationUpdate={(page, limit) => {
          const params = new URLSearchParams(searchParams);
          params.set('page', !page ? '1' : (page + 1).toString());
          if (params.get('limit') !== limit.toString()) {
            params.set('limit', limit.toString());
            params.set('page', '1');
          }
          router.push(`?${params.toString()}`);
        }}
      />
    </div>
  );
};

export default QueuesTable;
