import { useEffect, useState, useMemo, useRef } from 'react';
import {
  Typography,
  Chip,
  Checkbox,
  Tooltip,
  CircularProgress,
  DialogContentText,
  IconButton,
} from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSettings } from '@core/hooks/useSettings';
import { createColumnHelper, ColumnDef } from '@tanstack/react-table';
import apiClient from '@/utils/axios';
import DataTable from '@/components/Datatable';
import { toast } from 'react-toastify';
import { Product } from '@/types/productTypes';
import { RoleProtected } from '@/components/ProtectedRoleWrapper';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import { useSelector, useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '@/redux-store';
import { setProductsFilters, setProducts } from '@/redux-store/stores/ProductSku.store';
import { FieldsType, Options } from '@/redux-store/stores/common.store';
import FilterModal from '@/components/Modals/FilterModal';
import CustomIconButton from '@/components/CustonIconButton';
import { fetchFilteredProductsSkus } from '@/actions';
import {
  fetchProducts,
  updateProduct,
  fetchFilteredProductsSkusAdvanced,
} from '@/actions/products';
import { extractPaginationParams } from '@/utils/paginationParamHelper';

// Define the interface locally since it's not exported from the store
interface UpdateProductsParams {
  id: string;
  value: any;
}

const columnHelper = createColumnHelper<Product>();

function formatShopifyNativeVariant(value: any): string {
  if (!value) return '-';

  try {
    if (Array.isArray(value)) {
      return value
        .map(item => {
          if (typeof item === 'object' && item !== null) {
            return Object.values(item).filter(Boolean).join(', ');
          }
          return String(item);
        })
        .join('; ');
    }

    if (typeof value === 'object' && value !== null) {
      return Object.values(value).filter(Boolean).join(', ');
    }

    return String(value);
  } catch (e) {
    console.error('Error formatting shopifyNativeVariant:', e, value);
    return '[Format Error]';
  }
}

function parseCategoryData(categoryData: any): string {
  if (!categoryData) return '-';
  if (Array.isArray(categoryData)) {
    return [...new Set(categoryData)].join(', ');
  }
  if (typeof categoryData !== 'string') {
    return String(categoryData);
  }
  const matches = categoryData.match(/[A-Z][a-z]+/g);
  if (matches) {
    return [...new Set(matches)].join(', ');
  }
  return categoryData;
}

export const generateDynamicColumns = (fields: FieldsType[], router: any): ColumnDef<Product>[] => {
  return fields?.map(
    ({ key, label, type, options, secondary_key, sortable, action }: FieldsType | any) => {
      if (key === 'shopifyNativeVariant') {
        return {
          accessorKey: key as string[],
          header: label as string,
          enableColumnFilter: false,
          enableSorting: sortable ?? false,
          meta: { filterType: type, options },
          cell: (info: any) => {
            const value = info.getValue();
            const formattedValue = formatShopifyNativeVariant(value);

            return (
              <Tooltip title={formattedValue} arrow>
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    cursor: 'default',
                  }}
                >
                  {formattedValue}
                </Typography>
              </Tooltip>
            );
          },
        } as ColumnDef<Product>;
      }
      if (key === 'products.category') {
        return {
          accessorKey: key as string[],
          header: label as string,
          enableColumnFilter: false,
          enableSorting: sortable ?? false,
          meta: { filterType: type, options },
          cell: (info: any) => {
            const rowData = info.row.original;
            const categoryData = rowData.products_category;
            const displayValue = parseCategoryData(categoryData);

            return (
              <Tooltip title={displayValue}>
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {displayValue}
                </Typography>
              </Tooltip>
            );
          },
        } as ColumnDef<Product>;
      }
      if (key === 'products') {
        return {
          accessorKey: key as string[],
          header: label as string,
          enableColumnFilter: false,
          enableSorting: sortable ?? false,
          meta: { filterType: type, options },
          cell: (info: any) => {
            const value = info.getValue();
            let displayValue = '-';

            if (Array.isArray(value)) {
              displayValue = value.join(', ');
            } else if (typeof value === 'string') {
              displayValue = value;
            }

            return (
              <Tooltip title={displayValue}>
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {displayValue}
                </Typography>
              </Tooltip>
            );
          },
        } as ColumnDef<Product>;
      }

      return {
        accessorKey: key as string[],
        header: label as string,
        enableColumnFilter: false,
        enableSorting: sortable ?? false,
        meta: { filterType: type, options },
        cell: (info: any) => {
          const value = info.getValue();

          if (type === 'number') {
            return (
              <Typography
                sx={
                  action && {
                    cursor: 'pointer',
                    color: 'primary.main',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }
                }
                onClick={() => {
                  if (action && action?.route)
                    router.push(`${action.route}/${action.key && info.row.original[action.key]}`);
                }}
              >
                {value ?? '-'}
              </Typography>
            );
          }

          if (type === 'select' && options) {
            const optionLabel =
              options?.find((opt: Options) => opt.value === value)?.key || String(value);
            const chipColor =
              value === 'true' || value === true
                ? 'success'
                : value === 'false' || value === false
                  ? 'error'
                  : 'default';
            return (
              <Chip
                label={optionLabel}
                size="small"
                variant="tonal"
                color={chipColor}
                className="capitalize"
              />
            );
          }

          if (type === 'multi_select') {
            return Array.isArray(value) ? (
              <Tooltip title={String(value)}>
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    cursor: 'default',
                  }}
                >
                  {secondary_key ? (value.length > 0 ? value.join(',') : value.join(',')) : value}
                </Typography>
              </Tooltip>
            ) : (
              <Tooltip title={String(value || '-')}>
                <Typography
                  noWrap
                  sx={{
                    maxWidth: 200,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {value || '-'}
                </Typography>
              </Tooltip>
            );
          }

          return (
            <Typography noWrap sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {String(value ?? '-')}
            </Typography>
          );
        },
      } as ColumnDef<Product>;
    },
  );
};

const ProductSkuListTable = () => {
  const products = useSelector((state: RootState) => {
    return state.productSku;
  });
  const configs = useSelector((state: RootState) => state.common.tableConfig);
  const [statusModal, setStatusModal] = useState<UpdateProductsParams | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { updatePageSettings } = useSettings();
  const searchParams = useSearchParams();
  const prevFetchRef = useRef('');
  const hasRenderedWithData = useRef(false);

  const dynamicColumns = useMemo(() => {
    if (!configs?.fields || configs.fields.length === 0) {
      return [];
    }

    return generateDynamicColumns(configs.fields, router);
  }, [configs?.fields, router]);
  const columns = useMemo<ColumnDef<Product>[]>(() => {
    const selectColumn = columnHelper.display({
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          indeterminate={table.getIsSomePageRowsSelected()}
          onChange={table.getToggleAllPageRowsSelectedHandler()}
          onClick={e => e.stopPropagation()}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          disabled={!row.getCanSelect()}
          onChange={e => {
            e.stopPropagation();
            row.getToggleSelectedHandler()(e);
          }}
          onClick={e => e.stopPropagation()}
        />
      ),
      enableColumnFilter: false,
      size: 48,
    }) as ColumnDef<Product>;

    const actionColumn = columnHelper.display({
      id: 'action',
      header: 'Actions',
      cell: ({ row }) => {
        return (
          <div className="flex items-center">
            <Tooltip title="View Product">
              <CustomIconButton
                icon={<i className="ri-eye-line text-textSecondary" />}
                navigationRoute={`/ordermanagement/products/view/${row.original.id}`}
                ButtonAction={Actions.ViewDetailPage}
                actionTarget={ActionsTarget.PIMS}
              />
            </Tooltip>
            <Tooltip title="Edit Product">
              <CustomIconButton
                ButtonAction={Actions.EditDetailPage}
                actionTarget={ActionsTarget.PIMS}
                icon={<i className="ri-edit-line text-textSecondary" />}
                navigationRoute={`/ordermanagement/products/view/${row.original.id}/?isEdit=true`}
              />
            </Tooltip>
            <RoleProtected action={Actions.ActivateDeactivateSKU} actionTarget={ActionsTarget.PIMS}>
              <Tooltip title={row.original.isActive ? 'Deactivate Product' : 'Activate Product'}>
                <IconButton
                  size="small"
                  color={row.original.isActive ? 'error' : 'success'}
                  onClick={e => {
                    e.stopPropagation();
                    setStatusModal({
                      id: row.original.id,
                      value: { isActive: !row.original.isActive },
                    });
                  }}
                  sx={{ ml: 1, opacity: 1 }}
                >
                  <i className={row.original.isActive ? 'ri-toggle-line' : 'ri-toggle-fill'} />
                </IconButton>
              </Tooltip>
            </RoleProtected>
          </div>
        );
      },
    });

    return [selectColumn, ...dynamicColumns, actionColumn];
  }, [dynamicColumns]);

  useEffect(() => {
    const paramsObj = extractPaginationParams(searchParams);
    const page = Number(searchParams.get('page')) || 1;
    const limit = Number(searchParams.get('limit')) || 25;
    const flatFilters = Array.isArray(products.filters) ? products.filters.flat() : [];
    if (flatFilters.length > 0) {
      fetchFilteredProductsSkusAdvanced({
        filters: products.filters,
        page,
        limit,
      }).then(data => {
        dispatch(setProducts(data));
      });
    } else {
      fetchProducts(paramsObj).then(data => {
        dispatch(setProducts(data));
      });
    }
  }, [dispatch, searchParams, products.filters]);

  useEffect(() => {
    return updatePageSettings({
      skin: 'default',
    });
  }, []);

  useEffect(() => {
    if (products.data?.length > 0 && !hasRenderedWithData.current) {
      hasRenderedWithData.current = true;
    }
  }, [products.data, dynamicColumns]);

  useEffect(() => {
    prevFetchRef.current = '';
  }, [products.filters]);

  const handleRowClick = (row: Product) => {
    document.addEventListener(
      'click',
      function clickHandler(event) {
        document.removeEventListener('click', clickHandler);

        const target = event.target as HTMLElement;
        if (target.closest('.MuiCheckbox-root')) {
          return;
        }

        const path = `/ordermanagement/products/view/${row.id}`;

        if (event.ctrlKey || event.metaKey) {
          window.open(path, '_blank', 'noopener,noreferrer');
        } else {
          router.push(path);
        }
      },
      { once: true },
    );
  };

  // Filter handler
  const handleApplyFilters = async (normalizedFilters: any) => {
    try {
      setLoading(true);
      const isReset = !normalizedFilters || normalizedFilters.length === 0;

      if (isReset) {
        dispatch(setProductsFilters([]));
        // Reset page to 1 in the URL
        const url = new URL(window.location.href);
        url.searchParams.set('page', '1');
        window.history.replaceState({}, '', url.toString());
        // Fetch all products without filters
        const paramsObj = extractPaginationParams(searchParams);
        const data = await fetchProducts(paramsObj);
        dispatch(setProducts(data));
        setLoading(false);
        return null;
      } else {
        // Dispatch filters to Redux first
        dispatch(setProductsFilters(normalizedFilters));
        const data = await fetchFilteredProductsSkus({
          filters: normalizedFilters,
          pageNo: Number(searchParams.get('page')) || 1,
          limit: Number(searchParams.get('limit')) || 25,
        });
        dispatch(setProducts(data));
        setLoading(false);
        return data;
      }
    } catch (error) {
      setLoading(false);
      toast.error('Filter operation failed');
      return null;
    }
  };

  const handleUpdateProductStatus = async () => {
    setLoading(true);
    try {
      await updateProduct(statusModal as UpdateProductsParams);

      // Reload data after successful update
      const paramsObj = extractPaginationParams(searchParams);
      const page = Number(searchParams.get('page')) || 1;
      const limit = Number(searchParams.get('limit')) || 25;
      const flatFilters = Array.isArray(products.filters) ? products.filters.flat() : [];

      if (flatFilters.length > 0) {
        const data = await fetchFilteredProductsSkusAdvanced({
          filters: products.filters,
          page,
          limit,
        });
        dispatch(setProducts(data));
      } else {
        const data = await fetchProducts(paramsObj);
        dispatch(setProducts(data));
      }

      setLoading(false);
      setStatusModal(null);
      toast.success('Updated Successfully!');
    } catch (e) {
      setLoading(false);
      toast.error('Update failed!');
    }
  };

  return (
    <>
      <DataTable
        data={products.data || []}
        columns={columns}
        loading={products.loading}
        enableFilters
        endpoint="ordermanagement/products"
        totalCount={products.count}
        filterCount={products.filters?.flat(2)?.length || 0}
        enableBulkUpdate={true}
        bulkDataFields={configs?.bulkInsertFields}
        filterFields={configs?.filterDataFields}
        onRowClick={handleRowClick}
        onApplyFilters={handleApplyFilters}
        onBulkApply={ids => {
          return apiClient.patch('/product-sku/bulk-update', ids);
        }}
        reloadData={async () => {
          const paramsObj = extractPaginationParams(searchParams);
          const page = Number(searchParams.get('page')) || 1;
          const limit = Number(searchParams.get('limit')) || 25;
          const flatFilters = Array.isArray(products.filters) ? products.filters.flat() : [];
          if (flatFilters.length > 0) {
            const data = await fetchFilteredProductsSkusAdvanced({
              filters: products.filters,
              page,
              limit,
            });
            dispatch(setProducts(data));
          } else {
            const data = await fetchProducts(paramsObj);
            dispatch(setProducts(data));
          }
        }}
        onLimitChange={(newLimit: number) => {
          const url = new URL(window.location.href);
          url.searchParams.set('limit', String(newLimit));
          url.searchParams.set('page', '1');
          window.history.replaceState({}, '', url.toString());
        }}
      />
      <FilterModal
        open={statusModal ? true : false}
        onClose={() => {
          setStatusModal(null);
        }}
        children={
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to {statusModal?.value?.isActive ? 'Activate' : 'Deactivate'} the
            ProductSKU ?
          </DialogContentText>
        }
        title="Update SKU Status"
        buttons={[
          {
            title: 'Cancel',
            buttonProps: {
              onClick: () => setStatusModal(null),
              color: 'secondary',
            },
          },
          {
            title: loading ? <CircularProgress size={20} color="inherit" /> : 'Proceed',
            buttonProps: {
              onClick: handleUpdateProductStatus,
              variant: 'contained',
            },
          },
        ]}
      />
    </>
  );
};

export default ProductSkuListTable;
