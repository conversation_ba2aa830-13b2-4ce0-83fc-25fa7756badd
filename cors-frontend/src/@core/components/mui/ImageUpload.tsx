'use client';
import React, { useState } from 'react';
import { Controller, Control, FieldErrors, UseFormSetValue, useWatch } from 'react-hook-form';
import { Button, Box, Typography, FormHelperText, CircularProgress } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteIcon from '@mui/icons-material/Delete';
import Grid from '@mui/material/Grid2';
import { UploadImage } from '@/actions/image-upload';

interface ImageUploadFieldProps {
  control: Control<any>;
  errors: FieldErrors;
  setValue: UseFormSetValue<any>;
  name: string;
  minImages?: number;
  maxImages?: number;
  maxFileSizeMB?: number;
  allowedFileTypes?: string[];
  title?: string;
  buttonText?: string;
  errorField?: string;
  formValue?: string[];
  preview?: boolean;
  onChange?: (value?: string[]) => void;
  uploaderType?: 'image' | 'file';
}

const ImageUploadField: React.FC<ImageUploadFieldProps> = ({
  control,
  errors,
  setValue,
  name,
  minImages = 1,
  maxImages = 1,
  maxFileSizeMB = 10,
  allowedFileTypes = ['image/jpeg', 'image/png', 'image/gif'],
  title = 'Upload Images',
  buttonText = 'Select Images',
  errorField = '',
  formValue = [],
  preview = true,
  uploaderType = 'image',
  onChange: onChangeProp,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const validateFile = (file: File): boolean => {
    if (!allowedFileTypes.includes(file.type)) {
      setError(`File type not allowed. Allowed types: ${allowedFileTypes.join(', ')}`);
      return false;
    }

    if (file.size > maxFileSizeMB * 1024 * 1024) {
      setError(`File size must be less than ${maxFileSizeMB}MB`);
      return false;
    }

    return true;
  };
  const formValueWatch = useWatch({
    control,
    name: name,
  });

  const uploadImage = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      return await UploadImage(formData);
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error('Failed to upload image');
    }
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: string[]) => void,
  ) => {
    const files = event.target.files;
    if (!files?.length) return;

    setError('');
    setLoading(true);

    try {
      const fileArray = Array.from(files);
      const validFiles = fileArray.filter(validateFile);

      if (!validFiles?.length) {
        setLoading(false);
        return;
      }

      const remainingSlots = maxImages - formValue?.length;
      const filesToUpload = validFiles.slice(0, remainingSlots);

      const uploadedUrls = await Promise.all(filesToUpload.map(uploadImage));
      const newImages = [...formValue, ...uploadedUrls];
      onChangeProp && onChangeProp(newImages);
      onChange(newImages);
      setValue(name, newImages, { shouldValidate: true });
    } catch (error) {
      setError('Failed to upload images. Please try again.');
      console.error('Upload error:', error);
    } finally {
      setLoading(false);
    }
  };

  const removeImage = (index: number, onChange: (value: string[]) => void) => {
    const newImages = formValue.filter((_, i) => i !== index);
    onChangeProp && onChangeProp();
    onChange(newImages);
    setValue(name, newImages, { shouldValidate: true });
  };

  return (
    <Box sx={{ mb: 2, width: '100%' }}>
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Box>
            <Box sx={{ mb: 2 }}>
              <Button
                variant="contained"
                component="label"
                startIcon={
                  loading ? <CircularProgress size={20} color="inherit" /> : <CloudUploadIcon />
                }
                disabled={loading || formValue?.length >= maxImages}
                fullWidth
                sx={{ maxWidth: 250 }}
              >
                {loading ? 'Uploading...' : buttonText}
                <input
                  type="file"
                  hidden
                  multiple={maxImages > 1}
                  accept={allowedFileTypes.join(',')}
                  onChange={e => handleFileChange(e, field.onChange)}
                  onClick={e => {
                    // Reset file input
                    (e.target as HTMLInputElement).value = '';
                  }}
                />
              </Button>

              <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                {uploaderType === 'image'
                  ? `${formValue?.length} of ${maxImages} image(s) selected (Min: ${minImages})`
                  : `${formValue?.length} of ${maxImages} file(s) selected (Min: ${minImages})`}
              </Typography>
            </Box>

            {(error || errors[name] || errorField) && (
              <FormHelperText error>
                {error || errorField || (errors[name]?.message as string)}
              </FormHelperText>
            )}

            <Grid container spacing={2}>
              {preview &&
                formValueWatch?.map((url: string, idx: number) => (
                  <>
                    {url && (
                      <Grid size={{ xs: 6, sm: 2, md: 2 }} key={idx}>
                        <Box
                          sx={{
                            position: 'relative',
                            paddingTop: '100%',
                            borderRadius: 1,
                            border: '1px solid #ccc',
                            overflow: 'hidden',
                          }}
                        >
                          <img
                            src={url}
                            alt={`Image ${idx + 1}`}
                            style={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                          />
                          <Button
                            variant="contained"
                            color="error"
                            size="small"
                            onClick={() => removeImage(idx, field.onChange)}
                            sx={{
                              position: 'absolute',
                              top: 8,
                              right: 8,
                              minWidth: 'auto',
                              width: 32,
                              height: 32,
                              p: 0,
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </Button>
                        </Box>
                      </Grid>
                    )}
                  </>
                ))}
            </Grid>
          </Box>
        )}
      />
    </Box>
  );
};

export default ImageUploadField;
