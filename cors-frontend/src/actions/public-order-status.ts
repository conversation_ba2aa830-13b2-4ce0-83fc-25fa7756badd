'use server'

import { PublicOrderStatus } from '@/types/public-order-status.types';
import { handlePublicApiError } from '@/utils/errorHandler';
import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL;
const accessToken = process.env.NEXT_PUBLIC_CUSTOMER_VALIDATION_TOKEN;
const shopifyToken = process.env.SHOPIFY_TOKEN;

function createPublicApiClient() {
  return axios.create({
    baseURL: API_URL,
    withCredentials: false,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
}

function createCustomerUploadApiClient() {
  return axios.create({
    baseURL: API_URL,
    withCredentials: false,
    headers: {
      Authorization: `Bearer ${shopifyToken}`,
    },
  });
}

export async function fetchPublicOrderStatus(
  orderNumber: string,
  customerEmail?: string,
): Promise<PublicOrderStatus> {
  try {
    const publicApiClient = createPublicApiClient();
    const requestBody = {
      orderNumber: orderNumber,
      email: customerEmail || '',
    };
    const response = await publicApiClient.post('/order-tracking', requestBody);
    if (!response.data) {
      throw new Error('No data returned from server');
    }
    return response.data;
  } catch (error) {
    handlePublicApiError(error, 'fetch order status');
  }
}

export async function fetchRejectedImage(
  orderNumber: string,
  email: string,
  itemNumber: string
): Promise<any> {
  try {
    const publicApiClient = createPublicApiClient();
    const response = await publicApiClient.get('/order-tracking/rejected-image', {
      params: {
        orderNumber,
        email,
        itemNumber,
      },
    });

    if (!response.data) {
      throw new Error('No data returned from server');
    }
    return response.data;
  } catch (error) {
    handlePublicApiError(error, 'fetch rejected image');
  }
}

export async function publicApiCall<T>(
  method: string,
  endpoint: string,
  data?: any,
  params?: any,
): Promise<T> {
  try {
    const publicApiClient = createPublicApiClient();
    const response = await publicApiClient({
      method,
      url: endpoint,
      data,
      params,
    });
    return response?.data;
  } catch (error: any) {
    console.error(`Public API Error (${endpoint}):`, error?.message);
    handlePublicApiError(error, `API call to ${endpoint}`);
  }
}

export async function uploadNewImages({
  orderNumber,
  email,
  uploads,
}: {
  orderNumber: string;
  email: string;
  uploads: { rejectedAttachmentId: string; imageUrl: string }[];
}) {
  try {
    const publicApiClient = createPublicApiClient();
    const response = await publicApiClient.post('/order-tracking/upload-new-images', {
      orderNumber,
      email,
      uploads,
    });
    return response.data;
  } catch (error) {
    handlePublicApiError(error, 'upload new images');
  }
}

export async function uploadImageCustomer(formData: FormData): Promise<string> {
  try {
    const customerUploadApiClient = createCustomerUploadApiClient();
    const response = await customerUploadApiClient.post('/attachments/upload', formData);
    if (!response.data.url) {
      throw new Error('Failed to upload image');
    }
    return response?.data?.url;
  } catch (error) {
    console.error('Error uploading customer image:', error);
    handlePublicApiError(error, 'upload customer image');
  }
}
