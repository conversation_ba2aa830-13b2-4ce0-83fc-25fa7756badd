import { BadRequestException } from '@nestjs/common';
import { STATUS_TRANSITIONS } from 'src/common/line-item-status.transitions';
import { ATTACHMENT_STATUS_TRANSITIONS } from 'src/common/attachment-status.transitions';
import { User } from 'src/users/entities/user.entity';
import { accessEnv } from '../env.validation';


const SHOPIFY_TOKEN = accessEnv('SHOPIFY_TOKEN');

export function checkStatusTransition(
  currentStatus: string,
  newStatus: string,
) {
  const allowedStatuses = STATUS_TRANSITIONS[currentStatus];
  if (!allowedStatuses || !allowedStatuses.includes(newStatus)) {
    throw new BadRequestException(
      `Cannot transition from "${currentStatus}" to "${newStatus}"`,
    );
  }
}

export function checkAttachmentStatusTransition(
  currentStatus: string,
  newStatus: string,
) {
  const allowedStatuses = ATTACHMENT_STATUS_TRANSITIONS[currentStatus];
  if (!allowedStatuses || !allowedStatuses.includes(newStatus)) {
    throw new BadRequestException(
      `Cannot transition from "${currentStatus}" to "${newStatus}"`,
    );
  }
}

export function userFullName(user: User) {
  if (!user) return '';
  const firstName = user.firstName || '';
  const lastName = user.lastName || '';
  return `${firstName} ${lastName}`.trim();
}

export function verifyShopifyToken(headers: Record<string, any>): boolean {
  validateShopifyToken(headers, SHOPIFY_TOKEN);
  return true;
}

export function validateShopifyToken(headers: Record<string, any>, shopifyToken: string): boolean {

  if (shopifyToken === headers['api-token']) {
    return true;
  }
  throw new BadRequestException('Invalid API token');
}
