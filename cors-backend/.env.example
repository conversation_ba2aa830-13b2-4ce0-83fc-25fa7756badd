NODE_ENV=development
PORT=3000

DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=cors-development
DATABASE_SSL_ENABLED=false

JWT_SECRET=my-test-jwt-secret
JWT_ACCESS_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=1d
SHOPIFY_CUSTOMER_TOKEN= "YWlycGxhbmVzYX"
CUTOUT_PRO_API_KEY=jskdfjhkdsjhfkjsdhkfh
CUTOUT_PRO_API_URL=https://api.example.com/v1/

SWAGGER_USERNAME=admin
SWAGGER_PASSWORD=admin

RESET_ACCESS_EXPIRES_IN=1h
NODE_ENV=development
BASE_URL=http://localhost:3000


# SMTP Configuration
SMTP_HOST=sandbox.smtp.mailtrap.io
SMTP_PORT=587
SMTP_USERNAME=a9d2d3f0164e27
SMTP_PASSWORD=1f2bd67ba4bbaa
SMTP_FROM_EMAIL=<EMAIL>
FRONTEND_URL=https://cors-dev.cuddleclones.com

CLOUDINARY_CLOUD_NAME=cors-bucket
CLOUDINARY_API_KEY=858785646972144
CLOUDINARY_API_SECRET=f6R6_2rc9KGsVuZ8c23_AnNKFUk
STORAGE_DRIVER=cloudinary

SHOPIFY_ACCESS_TOKEN=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
SHOPIFY_STORE=cuddleclones-dev
SHOPIFY_API_VERSION=2025-04
SHOPIFY_WEBHOOK_SECRET=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


SHOPIFY_STORE_URL=https://cuddleclones-dev.myshopify.com/admin/api/2025-04
SHOPIFY_API_SECRET=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
SHOPIFY_STORE_NAME=cuddleclones-dev
NGROK_URL=https://3b68-2404-3100-1804-27e9-cbaa-1029-c734-5ea4.ngrok-free.app

BULLMQ_USERNAME=admin
BULLMQ_PASSWORD=CORS123
REDIS_HOST=settled-vervet-48506.upstash.io
REDIS_PASSWORD=Ab16AAIjcDE0NDIxYzZkZDIwZjM0NzAzODYzOWFjNGFlZjBkZTgzOHAxMA
REDIS_SSL_ENABLED=true
REDIS_PORT=6379
SHOPIFY_TOKEN=jdfslsdlflsdkfjlkdsjlfkjl
